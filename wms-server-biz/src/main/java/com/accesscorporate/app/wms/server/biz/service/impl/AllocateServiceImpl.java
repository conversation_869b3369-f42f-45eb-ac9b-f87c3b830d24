package com.accesscorporate.app.wms.server.biz.service.impl;

import com.accesscorporate.app.wms.server.biz.manager.impl.DocAlcDetailManager;
import com.accesscorporate.app.wms.server.biz.manager.impl.DocAlcHeaderManager;
import com.accesscorporate.app.wms.server.biz.mq.message.AllocateMessage;
import com.accesscorporate.app.wms.server.biz.service.AllocateService;
import com.accesscorporate.app.wms.server.biz.service.DocDoHeaderService;
import com.accesscorporate.app.wms.server.biz.service.IMdSkuService;
import com.accesscorporate.app.wms.server.biz.service.StockService;
import com.accesscorporate.app.wms.server.biz.dto.Stock2AllocateDTO;
import com.accesscorporate.app.wms.server.biz.utils.Config;
import com.accesscorporate.app.wms.server.biz.util.AllocateHelper;
import com.accesscorporate.app.wms.server.common.constant.Constants;
import com.accesscorporate.app.wms.server.common.enums.Keys;
import com.accesscorporate.app.wms.server.dal.entity.DocAlcDetail;
import com.accesscorporate.app.wms.server.dal.entity.DocAlcHeader;
import com.accesscorporate.app.wms.server.dal.entity.MdSku;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自动分配服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class AllocateServiceImpl implements AllocateService {

    @Resource
    private DocAlcHeaderManager docAlcHeaderManager;
    @Resource
    private DocAlcDetailManager docAlcDetailManager;
    @Resource
    private DocDoHeaderService deliveryOrderService;
    @Resource
    private IMdSkuService skuService;
    @Resource
    private StockService stockService;
    @Resource
    private AllocateHelper allocateHelper;

    @Override
    public void processAllocate(AllocateMessage allocateMessage) {
//        log.info("开始处理分配任务，仓库ID:{}, 批次大小:{}",
//            allocateMessage.getWarehouseId(), allocateMessage.getBatchSize());
//
//        try {
//            // 执行具体的分配逻辑
//            executeAllocateByWarehouse(allocateMessage.getWarehouseId(), allocateMessage.getBatchSize());
//
//            log.info("分配任务处理完成，仓库ID:{}", allocateMessage.getWarehouseId());
//
//        } catch (Exception e) {
//            log.error("分配任务处理失败，仓库ID:{}", allocateMessage.getWarehouseId(), e);
//            throw e;
//        }
    }

    @Override
    public void executeAllocateByWarehouse(Long warehouseId, Integer batchSize) {
        log.info("执行仓库分配逻辑，仓库ID:{}, 批次大小:{}", warehouseId, batchSize);
        
        try {
            // TODO: 实现具体的分配业务逻辑
            // 1. 查询待分配的订单/出库单
            // 2. 根据分配规则进行库存分配
            // 3. 更新分配结果
            
            // 示例逻辑框架：
            processAllocateOrders(warehouseId, batchSize);
            
        } catch (Exception e) {
            log.error("执行仓库分配逻辑失败，仓库ID:{}", warehouseId, e);
            throw e;
        }
    }

    @Override
    public List<Long> queryNeedAllocateDoHeaderIds(int batchAllocateNum, Long warehouseId) {
        //按照订单的导入时间进行排序（先进先分配）
        Integer failMaxNum = this.getAllocateFailNum();
        Integer failCheck = this.getFailCheckConfig();
        LambdaQueryWrapper<DocAlcHeader> wrapper = new LambdaQueryWrapper<DocAlcHeader>();
        wrapper.eq(DocAlcHeader::getWarehouseId, warehouseId)
                .eq(DocAlcHeader::getIsDeleted, Boolean.FALSE)
                .eq(DocAlcHeader::getReleaseStatus, Constants.ReleaseStatus.RELEASE.getValue())
                .in(DocAlcHeader::getStatus, Lists.newArrayList(Constants.DoStatus.INITIAL.getValue(), Constants.DoStatus.PARTALLOCATED.getValue() ))
                .in(DocAlcHeader::getDoType, Lists.newArrayList(Constants.DoType.SELL.getValue(), Constants.DoType.WHOLESALE.getValue()))
                .eq(failCheck==1,DocAlcHeader::getAllocateFailNum, failMaxNum)
                .and(wrapper1 -> wrapper1
                        .eq(DocAlcHeader::getAllocTime, "0000-00-00 00:00:00")
                        .or()
                        .isNull(DocAlcHeader::getAllocTime).or().eq(DocAlcHeader::getReplStatus, Constants.DoReplStatus.COMPLETE.getValue()))
                .orderByAsc(DocAlcHeader::getDoCreateTime)
        ;

        List<Long> alcHeaderIds = docAlcHeaderManager.list(new Page<>(0, batchAllocateNum),wrapper)
                .stream().map(DocAlcHeader::getId).collect(Collectors.toList());
        return alcHeaderIds;
    }
    @Transactional
    @Override
    public Map<String, List<String>> autoAllocate(Long alcId, List<Long> detailIds) throws Exception {
        if (alcId == null) {
            return Maps.newHashMap();
        }
        DocAlcHeader alcHeader = docAlcHeaderManager.getById(alcId);
        if (alcHeader == null){
            return Maps.newHashMap();
        }
        //DO取消拦截
        if (deliveryOrderService.doCancelIntercept(alcHeader.getId())){
            Map<String, List<String>> map =  Maps.newHashMap();
            map.put(DO_CANCEL,Lists.<String>newArrayList());
            return map;
        }

        assignAutoCheck(alcHeader);

        alcHeader.setReplStatus(Constants.DoReplStatus.NONE.getValue());

        List<DocAlcDetail> needFrozenAlcDetails = new ArrayList<DocAlcDetail>();
        Map<String, List<String>> allocateResult = Maps.newHashMap();

        // 查询分配明细
        LambdaQueryWrapper<DocAlcDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocAlcDetail::getDoHeaderId, alcHeader.getId())
               .eq(DocAlcDetail::getIsDeleted, Boolean.FALSE);
        List<DocAlcDetail> alcDetails = docAlcDetailManager.list(wrapper);

        // 执行分配逻辑
        allocateDetail(detailIds, alcHeader, alcDetails, needFrozenAlcDetails, allocateResult, null);

        // 检查订单状态
        checkDoStatusAfterAllocate(alcHeader);

        // 处理需要冻结的明细
        if (!needFrozenAlcDetails.isEmpty()) {
            autoFrozenOnAllocate(alcHeader.getId(), needFrozenAlcDetails, "ALLOC_LACK", "system");
        }

        return allocateResult;
    }
    @Transactional
    @Override
    public Map<String, List<String>> autoAllocate(Long doId) throws Exception {
        return autoAllocate(doId, Lists.<Long>newArrayList());
    }

    public Integer getAllocateFailNum(){
        return Config.getInt(Keys.Delivery.auto_allocate_job_allocate_fail_num, Config.ConfigLevel.GLOBAL, 5);
    }
    public Integer getFailCheckConfig(){
        Integer failCheck = Config.getInt(Keys.Delivery.auto_allocate_job_allocate_check_num, Config.ConfigLevel.GLOBAL, 1);
        return failCheck;
    }
    /**
     * 处理分配订单
     * 
     * @param warehouseId 仓库ID
     * @param batchSize 批次大小
     */
    private void processAllocateOrders(Long warehouseId, Integer batchSize) {
        log.info("开始处理分配订单，仓库ID:{}, 批次大小:{}", warehouseId, batchSize);
        
        // TODO: 根据实际业务需求实现以下逻辑：
        
        // 1. 查询待分配的出库单/订单
        // List<OutboundOrder> pendingOrders = queryPendingAllocateOrders(warehouseId, batchSize);
        
        // 2. 遍历处理每个订单
        // for (OutboundOrder order : pendingOrders) {
        //     try {
        //         // 执行分配逻辑
        //         allocateOrderInventory(order);
        //         
        //         // 更新订单状态
        //         updateOrderStatus(order);
        //         
        //     } catch (Exception e) {
        //         log.error("处理订单分配失败，订单ID:{}", order.getId(), e);
        //         // 可以选择继续处理下一个订单或者抛出异常
        //     }
        // }
        
        // 临时实现：模拟处理逻辑
        simulateAllocateProcess(warehouseId, batchSize);
        
        log.info("分配订单处理完成，仓库ID:{}", warehouseId);
    }
    
    /**
     * 模拟分配处理过程
     * TODO: 替换为实际的业务逻辑
     */
    private void simulateAllocateProcess(Long warehouseId, Integer batchSize) {
        log.info("模拟分配处理，仓库ID:{}, 批次大小:{}", warehouseId, batchSize);
        
        // 模拟处理时间
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        log.info("模拟分配处理完成，仓库ID:{}", warehouseId);
    }

    /**
     * 分配前的自动检查
     * @param alcHeader 分配头
     */
    private void assignAutoCheck(DocAlcHeader alcHeader) {
        if (alcHeader == null) {
            throw new RuntimeException("订单不存在");
        }

        // 只有初始化或者部分分配状态且释放状态的时候才能进行自动分配
        if (!(Constants.DoStatus.INITIAL.getValue().equals(alcHeader.getStatus()) ||
              Constants.DoStatus.PARTALLOCATED.getValue().equals(alcHeader.getStatus())) ||
            !Constants.ReleaseStatus.RELEASE.getValue().equals(alcHeader.getReleaseStatus())) {
            throw new RuntimeException("订单状态不允许分配");
        }
    }

    /**
     * 分配完成后检查订单状态是否需要进行更新
     * @param alcHeader 分配头
     */
    private void checkDoStatusAfterAllocate(DocAlcHeader alcHeader) {
        log.debug("Check Do Status:[id: {}, status: {}, replStatus: {}, allocTime: {}]",
                alcHeader.getId(), alcHeader.getStatus(), alcHeader.getReplStatus(), alcHeader.getAllocTime());

        // 查询分配明细
        LambdaQueryWrapper<DocAlcDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocAlcDetail::getDoHeaderId, alcHeader.getId())
               .eq(DocAlcDetail::getIsDeleted, Boolean.FALSE);
        List<DocAlcDetail> alcDetails = docAlcDetailManager.list(wrapper);

        int finishedCount = 0; // 分配完成的明细数量
        int needAllocateCount = 0; // 需要分配的明细数量
        boolean isPartAllocated = false;

        for (DocAlcDetail alcDetail : alcDetails) {
            if (Constants.DoStatus.PARTALLOCATED.getValue().equals(alcDetail.getLinestatus())) {
                isPartAllocated = true;
                break;
            } else if (alcDetail.getIsDoLeaf() != null && alcDetail.getIsDoLeaf().equals(1L)) {
                needAllocateCount++;
                if (Constants.DoStatus.ALLALLOCATED.getValue().equals(alcDetail.getLinestatus())) {
                    finishedCount++;
                }
            }
        }

        String doStatus;
        if (isPartAllocated) {
            doStatus = Constants.DoStatus.PARTALLOCATED.getValue();
        } else if (finishedCount == 0) {
            doStatus = Constants.DoStatus.INITIAL.getValue();
        } else if (finishedCount < needAllocateCount) {
            doStatus = Constants.DoStatus.PARTALLOCATED.getValue();
        } else {
            doStatus = Constants.DoStatus.ALLALLOCATED.getValue();
        }

        alcHeader.setStatus(doStatus);
        alcHeader.setAllocTime(LocalDateTime.now());
        docAlcHeaderManager.updateById(alcHeader);
    }

    /**
     * 分配明细逻辑
     * @param detailIds 明细ID列表
     * @param alcHeader 分配头
     * @param alcDetails 分配明细列表
     * @param needFrozenAlcDetails 需要冻结的明细
     * @param allocateResult 分配结果
     * @param region 区域
     */
    private void allocateDetail(List<Long> detailIds, DocAlcHeader alcHeader, List<DocAlcDetail> alcDetails,
                               List<DocAlcDetail> needFrozenAlcDetails, Map<String, List<String>> allocateResult, String region) {

        for (DocAlcDetail alcDetail : alcDetails) {
            if (detailIds != null && !detailIds.isEmpty() && !detailIds.contains(alcDetail.getId())) {
                continue;
            }

            try {
                // 开始分配前清空待补货数据
                alcDetail.setNeedReplQty(BigDecimal.ZERO);
                alcDetail.setNoStockFlag(Constants.YesNo.NO.getValue());

                String result = executeAllocate(alcDetail, region);
                if (!ALC_RESULT_SUCCESS.equals(result)) {
                    if (allocateResult.get(result) == null) {
                        allocateResult.put(result, Lists.newArrayList("SKU_" + alcDetail.getSkuId()));
                    } else {
                        allocateResult.get(result).add("SKU_" + alcDetail.getSkuId());
                    }
                    log.info("allocate result: {}", result);
                }

                if (NEED_REPL.equals(result)) {
                    alcHeader.setReplStatus(Constants.DoReplStatus.WAIT.getValue());
                    alcHeader.setReplStartTime(LocalDateTime.now());
                }

                docAlcDetailManager.updateById(alcDetail);
            } catch (Exception ex) {
                String exMsg = NO_ENOUGH_STOCK_QTY;
                if (allocateResult.get(exMsg) == null) {
                    allocateResult.put(exMsg, Lists.newArrayList("SKU_" + alcDetail.getSkuId()));
                } else {
                    allocateResult.get(exMsg).add("SKU_" + alcDetail.getSkuId());
                }

                needFrozenAlcDetails.add(alcDetail);
                alcHeader.setNoStockFlag(Constants.YesNo.YES.getValue());
                log.error("分配异常", ex);
            }
        }
    }

    /**
     * 执行分配逻辑
     * @param alcDetail 分配明细
     * @param region 区域
     * @return 分配结果
     */
    private String executeAllocate(DocAlcDetail alcDetail, String region) throws Exception {
        log.debug("开始执行分配逻辑，明细ID: {}, SKU ID: {}, 需求数量: {}", 
                alcDetail.getId(), alcDetail.getSkuId(), alcDetail.getExpectedQtyEach());
        
        // 如果已经全部分配，直接返回成功
        if (Constants.DoStatus.ALLALLOCATED.getValue().equals(alcDetail.getLinestatus())) {
            return ALC_RESULT_SUCCESS;
        }
        
        // 计算还需要分配的数量
        BigDecimal notAllocQty = alcDetail.getExpectedQtyEach().subtract(alcDetail.getAllocatedQtyEach());
        if (notAllocQty.compareTo(BigDecimal.ZERO) <= 0) {
            alcDetail.setLinestatus(Constants.DoStatus.ALLALLOCATED.getValue());
            return ALC_RESULT_SUCCESS;
        }
        
        // 只有isDoLeaf为1的明细才需要分配
        if (!Integer.valueOf(1).equals(alcDetail.getIsDoLeaf())) {
            return ALC_RESULT_SUCCESS;
        }
        
        DocAlcHeader alcHeader = docAlcHeaderManager.getById(alcDetail.getDoHeaderId());
        if (alcHeader == null) {
            return ALC_RESULT_NODO;
        }
        
        String doType = alcHeader.getDoType();
        // 根据订单类型选择分配策略
        if (Constants.DoType.WHOLESALE.getValue().equals(doType) ||
            Constants.DoType.ALLOT.getValue().equals(doType) ||
            Constants.DoType.RTV.getValue().equals(doType)) {
            return executeWholesaleAllocate(alcDetail, alcHeader, region, notAllocQty);
        } else if (Constants.DoType.SELL.getValue().equals(doType)) {
            return executeSellAllocate(alcDetail, alcHeader, region, notAllocQty);
        }
        
        return ALC_RESULT_NORULE;
    }

    /**
     * 执行批发分配
     * @param alcDetail 分配明细
     * @param alcHeader 分配头
     * @param region 区域
     * @param notAllocQty 未分配数量
     * @return 分配结果
     */
    private String executeWholesaleAllocate(DocAlcDetail alcDetail, DocAlcHeader alcHeader, String region, BigDecimal notAllocQty) throws Exception {
        log.info("执行批发分配逻辑，明细ID: {}, SKU ID: {}, 需求数量: {}", 
                alcDetail.getId(), alcDetail.getSkuId(), notAllocQty);
        
        // 检查库存是否足够
        BigDecimal availableStock = queryAvailableStock(alcDetail, alcHeader, region);
        
        if (availableStock.compareTo(notAllocQty) >= 0) {
            // 库存足够，执行分配
            doAllocateStock(alcDetail, notAllocQty);
            alcDetail.setLinestatus(Constants.DoStatus.ALLALLOCATED.getValue());
            return ALC_RESULT_SUCCESS;
        } else if (availableStock.compareTo(BigDecimal.ZERO) > 0) {
            // 部分分配
            doAllocateStock(alcDetail, availableStock);
            alcDetail.setLinestatus(Constants.DoStatus.PARTALLOCATED.getValue());
            return ALC_RESULT_SUCCESS;
        } else {
            // 没有库存，检查是否需要补货
            return checkNeedReplenish(alcDetail, alcHeader, region, notAllocQty);
        }
    }

    /**
     * 执行销售分配
     * @param alcDetail 分配明细
     * @param alcHeader 分配头
     * @param region 区域
     * @param notAllocQty 未分配数量
     * @return 分配结果
     */
    private String executeSellAllocate(DocAlcDetail alcDetail, DocAlcHeader alcHeader, String region, BigDecimal notAllocQty) throws Exception {
        log.info("执行销售分配逻辑，明细ID: {}, SKU ID: {}, 需求数量: {}", 
                alcDetail.getId(), alcDetail.getSkuId(), notAllocQty);
        
        // 销售订单可能需要考虑组合品等特殊逻辑
        // 这里先简化处理，和批发逻辑类似
        BigDecimal availableStock = queryAvailableStock(alcDetail, alcHeader, region);
        
        if (availableStock.compareTo(notAllocQty) >= 0) {
            // 库存足够，执行分配
            doAllocateStock(alcDetail, notAllocQty);
            alcDetail.setLinestatus(Constants.DoStatus.ALLALLOCATED.getValue());
            return ALC_RESULT_SUCCESS;
        } else if (availableStock.compareTo(BigDecimal.ZERO) > 0) {
            // 部分分配
            doAllocateStock(alcDetail, availableStock);
            alcDetail.setLinestatus(Constants.DoStatus.PARTALLOCATED.getValue());
            return ALC_RESULT_SUCCESS;
        } else {
            // 没有库存，检查是否需要补货
            return checkNeedReplenish(alcDetail, alcHeader, region, notAllocQty);
        }
    }

    /**
     * 查询可用库存
     * @param alcDetail 分配明细
     * @param alcHeader 分配头
     * @param region 区域
     * @return 可用库存数量
     */
    private BigDecimal queryAvailableStock(DocAlcDetail alcDetail, DocAlcHeader alcHeader, String region) {
        log.debug("查询可用库存，SKU ID: {}, 仓库ID: {}", alcDetail.getSkuId(), alcHeader.getWarehouseId());
        
        try {
            // 构建查询条件
            List<String> locTypes = allocateHelper.buildLocTypesForAllocate(alcHeader, alcDetail);
            
            // 查询可分配库存
            List<Stock2AllocateDTO> stockList = stockService.queryStockForAllocate(
                alcDetail.getSkuId(), 
                alcHeader.getWarehouseId(), 
                locTypes, 
                region, 
                alcHeader.getShopId()
            );
            
            // 计算总可用库存
            BigDecimal totalAvailable = BigDecimal.ZERO;
            for (Stock2AllocateDTO stock : stockList) {
                totalAvailable = totalAvailable.add(stock.getActQty()).add(stock.getPendingQty());
            }
            
            log.debug("SKU {} 可用库存总数: {}", alcDetail.getSkuId(), totalAvailable);
            return totalAvailable;
            
        } catch (Exception e) {
            log.error("查询可用库存失败，SKU ID: {}", alcDetail.getSkuId(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 执行库存分配
     * @param alcDetail 分配明细
     * @param allocQty 分配数量
     */
    private void doAllocateStock(DocAlcDetail alcDetail, BigDecimal allocQty) {
        log.info("执行库存分配，SKU ID: {}, 分配数量: {}", alcDetail.getSkuId(), allocQty);
        
        // 更新分配数量
        alcDetail.setAllocatedQtyEach(alcDetail.getAllocatedQtyEach().add(allocQty));
        
        try {
            // 查询可用库存
            List<String> locTypes = buildLocTypesForAllocate(alcDetail);
            List<Stock2AllocateDTO> stockList = stockService.queryStockForAllocate(
                alcDetail.getSkuId(), 
                alcDetail.getWarehouseId(), 
                locTypes, 
                null, 
                alcDetail.getShopId()
            );
            
            // 按分配规则排序库存
            sortStockForAllocate(stockList, alcDetail);
            
            // 分配库存
            BigDecimal remainingQty = allocQty;
            for (Stock2AllocateDTO stock : stockList) {
                if (remainingQty.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }
                
                // 计算本次可分配数量
                BigDecimal canAllocate = stock.getActQty().min(remainingQty);
                
                if (canAllocate.compareTo(BigDecimal.ZERO) > 0) {
                    // 执行库存分配
                    boolean success = stockService.allocateStock(
                        stock.getStockId(), 
                        canAllocate, 
                        "ALC_" + alcDetail.getDoHeaderId()
                    );
                    
                    if (success) {
                        remainingQty = remainingQty.subtract(canAllocate);
                        log.debug("成功分配库存，库存ID: {}, 数量: {}", stock.getStockId(), canAllocate);
                    } else {
                        log.warn("库存分配失败，库存ID: {}", stock.getStockId());
                    }
                }
            }
            
            if (remainingQty.compareTo(BigDecimal.ZERO) > 0) {
                log.warn("库存分配不足，SKU ID: {}, 未分配数量: {}", alcDetail.getSkuId(), remainingQty);
            }
            
        } catch (Exception e) {
            log.error("执行库存分配失败，SKU ID: {}", alcDetail.getSkuId(), e);
            throw new RuntimeException("库存分配失败", e);
        }
    }
    
    /**
     * 按分配规则排序库存
     * @param stockList 库存列表
     * @param alcDetail 分配明细
     */
    private void sortStockForAllocate(List<Stock2AllocateDTO> stockList, DocAlcDetail alcDetail) {
        // TODO: 实现库存排序逻辑
        // 根据分配规则对库存进行排序，如：
        // 1. 按效期排序（FIFO）
        // 2. 按库位类型排序
        // 3. 按批次属性排序等
        
        // 暂时不排序
    }

    /**
     * 检查是否需要补货
     * @param alcDetail 分配明细
     * @param alcHeader 分配头
     * @param region 区域
     * @param notAllocQty 未分配数量
     * @return 分配结果
     */
    private String checkNeedReplenish(DocAlcDetail alcDetail, DocAlcHeader alcHeader, String region, BigDecimal notAllocQty) {
        log.debug("检查是否需要补货，SKU ID: {}, 缺货数量: {}", alcDetail.getSkuId(), notAllocQty);
        
        try {
            // 检查是否配置了补货规则
            if (!isReplenishEnabled(alcHeader.getWarehouseId())) {
                // 未启用补货，直接返回库存不足
                alcDetail.setNeedReplQty(notAllocQty);
                alcDetail.setNoStockFlag(Constants.YesNo.YES.getValue());
                return NO_ENOUGH_STOCK_QTY;
            }
            
            // 查询存储区可用于补货的库存
            List<Stock2AllocateDTO> replenishStock = stockService.queryStockForReplenish(
                alcDetail.getSkuId(), 
                alcHeader.getWarehouseId(), 
                region
            );
            
            // 计算存储区总库存
            BigDecimal replenishQty = BigDecimal.ZERO;
            for (Stock2AllocateDTO stock : replenishStock) {
                replenishQty = replenishQty.add(stock.getActQty());
            }
            
            if (replenishQty.compareTo(notAllocQty) >= 0) {
                // 存储区库存足够，需要补货
                alcDetail.setNeedReplQty(notAllocQty);
                alcDetail.setNoStockFlag(Constants.YesNo.YES.getValue());
                return NEED_REPL;
            } else {
                // 存储区库存也不足
                alcDetail.setNeedReplQty(notAllocQty);
                alcDetail.setNoStockFlag(Constants.YesNo.YES.getValue());
                return NO_ENOUGH_STOCK_QTY;
            }
            
        } catch (Exception e) {
            log.error("检查补货失败，SKU ID: {}", alcDetail.getSkuId(), e);
            alcDetail.setNeedReplQty(notAllocQty);
            alcDetail.setNoStockFlag(Constants.YesNo.YES.getValue());
            return NO_ENOUGH_STOCK_QTY;
        }
    }
    
    /**
     * 检查是否启用了补货功能
     * @param warehouseId 仓库ID
     * @return 是否启用
     */
    private boolean isReplenishEnabled(Long warehouseId) {
        // TODO: 从配置中读取补货开关
        // return Config.getBoolean("replenish.enabled", Config.ConfigLevel.WAREHOUSE, warehouseId, false);
        return false;
    }

    /**
     * 自动冻结分配缺货的明细
     * @param doHeaderId 订单头ID
     * @param needFrozenAlcDetails 需要冻结的明细
     * @param reasonCode 原因代码
     * @param holdWho 冻结人
     */
    private void autoFrozenOnAllocate(Long doHeaderId, List<DocAlcDetail> needFrozenAlcDetails, String reasonCode, String holdWho) {
        log.info("自动冻结分配缺货明细，订单ID:{}, 明细数量:{}", doHeaderId, needFrozenAlcDetails.size());
        
        // 如果没有需要冻结的明细，直接返回
        if (CollectionUtils.isEmpty(needFrozenAlcDetails)) {
            return;
        }
        
        // 更新订单头状态为缺货
        DocAlcHeader alcHeader = docAlcHeaderManager.getById(doHeaderId);
        if (alcHeader == null) {
            log.warn("订单不存在，订单ID: {}", doHeaderId);
            return;
        }
        
        // 设置订单缺货标志
        alcHeader.setNoStockFlag(Constants.YesNo.YES.getValue());
        alcHeader.setHoldCode(reasonCode);
        alcHeader.setHoldWho(holdWho);
        alcHeader.setHoldTime(LocalDateTime.now());
        alcHeader.setReleaseStatus(Constants.ReleaseStatus.HOLD.getValue());
        docAlcHeaderManager.updateById(alcHeader);
        
        // 更新明细缺货状态
        for (DocAlcDetail detail : needFrozenAlcDetails) {
            detail.setNoStockFlag(Constants.YesNo.YES.getValue());
            // TODO: 创建缺货记录
            // createLackDetail(detail, reasonCode);
        }
        
        // 批量更新明细
        docAlcDetailManager.updateBatchById(needFrozenAlcDetails);
        
        // 如果配置了自动通知客服，则缺货自动通知客服
        String lackAutoAnnounceCsStr = Config.getConfigValue("alloc.lack.autoAnnounceCs", Config.ConfigLevel.GLOBAL, "false");
        Boolean lackAutoAnnounceCs = "true".equalsIgnoreCase(lackAutoAnnounceCsStr) || "1".equals(lackAutoAnnounceCsStr);
        if (lackAutoAnnounceCs) {
            notifyCustomerService(doHeaderId, reasonCode);
        }
    }
    
    /**
     * 通知客服
     * @param doHeaderId 订单头ID
     * @param reasonCode 原因代码
     */
    private void notifyCustomerService(Long doHeaderId, String reasonCode) {
        try {
            log.info("发送缺货通知给客服，订单ID: {}, 原因代码: {}", doHeaderId, reasonCode);
            
            // TODO: 实现客服通知逻辑
            // 1. 构建通知DTO
            // 2. 调用客服通知接口
            
        } catch (Exception e) {
            log.error("通知客服失败，订单ID: {}", doHeaderId, e);
            // 通知失败不应影响主流程
        }
    }

    @Override
    public void releaseAssignedStock(Long doHeaderId, List<Long> doDetailIds) {
        log.info("释放已分配库存，DO头ID:{}, 明细ID列表:{}", doHeaderId, doDetailIds);

        // 查询分配明细
        LambdaQueryWrapper<DocAlcDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocAlcDetail::getDoHeaderId, doHeaderId)
               .in(DocAlcDetail::getId, doDetailIds)
               .eq(DocAlcDetail::getIsDeleted, Boolean.FALSE);
        List<DocAlcDetail> alcDetails = docAlcDetailManager.list(wrapper);

        // 释放库存逻辑
        for (DocAlcDetail detail : alcDetails) {
            // 重置分配数量
            detail.setAllocatedQtyEach(BigDecimal.ZERO);
            detail.setLinestatus(Constants.DoStatus.INITIAL.getValue());

            // 这里应该调用库存服务释放库存
            log.info("释放库存，SKU ID:{}, 释放数量:{}", detail.getSkuId(), detail.getExpectedQtyEach());
        }

        // 批量更新
        if (!alcDetails.isEmpty()) {
            docAlcDetailManager.updateBatchById(alcDetails);
        }
    }

    @Override
    public void removeAllocate(Long doId) {
        log.info("移除分配，DO ID:{}", doId);

        // 查询分配头
        DocAlcHeader alcHeader = docAlcHeaderManager.getById(doId);
        if (alcHeader != null) {
            // 重置分配状态
            alcHeader.setStatus(Constants.DoStatus.INITIAL.getValue());
            alcHeader.setAllocTime(null);
            alcHeader.setReplStatus(Constants.DoReplStatus.NONE.getValue());
            docAlcHeaderManager.updateById(alcHeader);
        }

        // 查询并重置分配明细
        LambdaQueryWrapper<DocAlcDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocAlcDetail::getDoHeaderId, doId)
               .eq(DocAlcDetail::getIsDeleted, Boolean.FALSE);
        List<DocAlcDetail> alcDetails = docAlcDetailManager.list(wrapper);

        for (DocAlcDetail detail : alcDetails) {
            detail.setAllocatedQtyEach(BigDecimal.ZERO);
            detail.setLinestatus(Constants.DoStatus.INITIAL.getValue());
            detail.setNeedReplQty(BigDecimal.ZERO);
            detail.setNoStockFlag(Constants.YesNo.NO.getValue());
        }

        if (!alcDetails.isEmpty()) {
            docAlcDetailManager.updateBatchById(alcDetails);
        }
    }


}
